import { z as zod } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import Stack from '@mui/material/Stack';
import LoadingButton from '@mui/lab/LoadingButton';

import { Form, Field } from 'src/components/hook-form';
import { useAuthContext } from 'src/auth/hooks';
import request from 'src/utils/request';
import { toast } from 'sonner';
import { getLoveRoleName } from 'src/utils/utils';

// ----------------------------------------------------------------------

export const CommentSchema = zod.object({
  comment: zod.string().min(1, { message: 'Comment is required!' }),
});

// ----------------------------------------------------------------------

export function YxCommentForm({ loveId, setCommentList }) {
  const defaultValues = { comment: '' };

  const { loveRole } = useAuthContext();

  const methods = useForm({
    resolver: zodResolver(CommentSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      const resp = await request(`/api/love/comment/persist`, {
        model: {
          loveId,
          content: data.comment,
          name: getLoveRoleName(loveRole),
        },
      });
      if (resp.status === 0) {
        toast.success('评论成功！');
        setCommentList((prev) => [...prev, resp.model]);
        reset();
      }
    } catch (error) {
      console.error(error);
    }
  });

  return (
    <Form methods={methods} onSubmit={onSubmit}>
      <Stack spacing={1}>
        <Field.Text name="comment" placeholder="说点什么..." enterKeyHint="send" />

        <Stack direction="row" alignItems="center">
          {/* <Stack direction="row" alignItems="center" flexGrow={1}>
            <IconButton>
              <Iconify icon="solar:gallery-add-bold" />
            </IconButton>

            <IconButton>
              <Iconify icon="eva:attach-2-fill" />
            </IconButton>

            <IconButton>
              <Iconify icon="eva:smiling-face-fill" />
            </IconButton>
          </Stack> */}

          <LoadingButton type="submit" variant="contained" loading={isSubmitting}>
            发送
          </LoadingButton>
        </Stack>
      </Stack>
    </Form>
  );
}
