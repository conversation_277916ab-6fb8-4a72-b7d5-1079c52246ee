import { Stack, Typography } from '@mui/material';

export function YxCommentList({ comments = [] }) {
  return (
    <Stack sx={{ mb: 1 }}>
      {comments?.map((comment, index) => {
        const { content, name, time } = comment;
        return (
          <Typography key={index} variant="body2" color="text.secondary">
            {name}: {content}
          </Typography>
        );
      })}
    </Stack>
  );
}
