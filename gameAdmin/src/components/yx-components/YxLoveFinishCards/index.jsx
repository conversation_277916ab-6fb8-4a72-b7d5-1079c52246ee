import {
  Bad<PERSON>,
  Box,
  Card,
  CardA<PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON>lapse,
  Divider,
  <PERSON>con<PERSON>utton,
  Pagination,
  Stack,
  styled,
  Typography,
} from '@mui/material';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import GdxImage from 'src/components/gdx/gdx-data/components/GdxImage';
import { Iconify } from 'src/components/iconify';
import request from 'src/utils/request';
import { YxCommentForm } from '../YxCommentForm';
import { YxCommentList } from '../YxCommentList';

export default function YxLoveFinishCards() {
  const [list, setList] = useState([]);
  const [pageNo, setPageNo] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  useEffect(() => {
    const fetchData = async () => {
      const resp = await request('/api/love/page', {
        query: {
          pageNo,
          pageSize: 20,
          status: 'FINISHED',
          sortOrder: -1,
          sortField: 'finishTime',
        },
      });
      if (resp.status === 0) {
        setList(resp.page.content);
        setTotalPages(resp.page.totalPages);
      }
    };
    fetchData();
  }, [pageNo]);
  return (
    <Stack spacing={1}>
      {list.map((item, index) => (
        <YxLoveFinishCard key={index} item={item} />
      ))}
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
        <Pagination
          count={totalPages}
          color="primary"
          onChange={(e, page) => setPageNo(page)}
          showFirstButton
          showLastButton
        />
      </Box>
    </Stack>
  );
}

const ExpandMore = styled((props) => {
  const { expand, ...other } = props;
  return <IconButton {...other} />;
})(({ theme }) => ({
  transition: theme.transitions.create('transform', {
    duration: theme.transitions.duration.shortest,
  }),
  variants: [
    {
      props: ({ expand }) => !expand,
      style: {
        transform: 'rotate(0deg)',
      },
    },
    {
      props: ({ expand }) => !!expand,
      style: {
        transform: 'rotate(180deg)',
      },
    },
  ],
}));

function YxLoveFinishCard({ item }) {
  const { id, title, yLog, xLog, photos, finishTime } = item;
  const [expanded, setExpanded] = useState(false);
  const [commentList, setCommentList] = useState([]);

  const handleExpandClick = () => {
    setExpanded(!expanded);
  };
  useEffect(() => {
    if (expanded) {
      const loadComments = async () => {
        const resp = await request(`/api/love/comment/list`, { query: { loveId: id } });
        if (resp.status === 0) {
          setCommentList(resp.list.reverse());
        }
      };
      loadComments();
    }
  }, [expanded]);
  return (
    <Card>
      <CardContent>
        <Typography variant="h5" color="pink">
          {title}
        </Typography>
        <Typography variant="body2" gutterBottom color="text.secondary">
          {dayjs(finishTime).format('YYYY-MM-DD')}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          小徐：{yLog || '好想小张～让我想想说些什么'}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          小张：{xLog || '好想小徐～让我想想说些什么'}
        </Typography>
        <GdxImage value={photos} size="small" />
      </CardContent>
      <Divider />
      <CardActions disableSpacing sx={{ justifyContent: 'flex-end' }}>
        <Box sx={{ mr: 1 }}>
          <Badge badgeContent={commentList.length} color="error">
            <ExpandMore
              expand={expanded}
              onClick={handleExpandClick}
              aria-expanded={expanded}
              aria-label="show more"
            >
              <Iconify icon="eva:arrow-ios-downward-fill" />
            </ExpandMore>
          </Badge>
        </Box>
      </CardActions>
      <Collapse in={expanded} timeout="auto" unmountOnExit>
        <CardContent sx={{ pt: 0 }}>
          <YxCommentList comments={commentList} />
          <YxCommentForm loveId={id} setCommentList={setCommentList} />
        </CardContent>
      </Collapse>
    </Card>
  );
}
