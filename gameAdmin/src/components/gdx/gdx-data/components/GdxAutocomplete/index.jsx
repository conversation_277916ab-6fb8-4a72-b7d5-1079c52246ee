import { useState, useEffect } from "react";

import { MenuItem, TextField, FormControl, Autocomplete } from "@mui/material";

import { sleep } from "src/utils/utils";

import { GdxItemMdWidth } from "../../const";
import { useGdxTypeData } from "../../context/hook";


export default function GdxAutocomplete({ label, mdWidth, allowEmpty = true, dataType, value, onChange, options, disabled, ...restProps }) {
    const [opts, setOpts] = useState();
    const { typeData } = useGdxTypeData({ type: dataType });
    const [_value, setValue] = useState("");
    const [loading, setLoading] = useState(false);
    useEffect(() => {
        if (typeData && typeData.options) {
            setOpts(typeData.options);
        } else if (options) {
            setOpts(options);
        }
    }, [options, typeData])
    useEffect(() => {
        if (value) {
            setValue(opts && opts.find((x) => x.id === value));
        }
    }, [opts, value])
    useEffect(() => {
        if (onChange) {
            onChange((_value && _value.id) || null);
        }
    }, [_value, onChange])
    const load = async () => {
        if (opts.length < 100) return;
        setLoading(true);
        await sleep(1000);
        setLoading(false);
    }
    if (!opts || !opts.length) return null;
    return (
        <FormControl
            sx={{
                flexShrink: 0,
                width: { xs: 1, md: mdWidth || GdxItemMdWidth },
            }}
            {...restProps}
        >
            <Autocomplete
                value={_value || null}
                onChange={(e, newValue) => {
                    setValue(newValue);
                }}
                onOpen={() => { load() }}
                options={loading ? [] : (opts || [])}
                getOptionLabel={(option) => option.name}
                renderOption={(props, option) => (
                    <MenuItem {...props}>
                        {option.name}
                    </MenuItem>
                )}
                loading={loading}
                renderInput={(params) => (
                    <TextField
                        {...params}
                        label={label}
                        onChange={load}
                        disabled={disabled}
                        sx={{ textTransform: 'capitalize' }}
                    />
                )}
            />
        </FormControl>
    )
}