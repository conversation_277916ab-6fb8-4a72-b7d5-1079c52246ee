import classNames from 'classnames';
import styles from './index.module.scss'


import { getResourceImageUrl } from '../../const';

export function GdxImageInner({ value, size = "medium" }) {
    return (
        <div className={classNames(styles.image, size)}>
            <img style={{ maxWidth: "100%", maxHeight: "100%" }} src={`${getResourceImageUrl(value)}`} alt={value || ""}></img>
        </div>
    )
}

export default function GdxImage({ value, ...restProps }) {
    if (!value) return null;
    if (Array.isArray(value)) {
        return value.map((x) => (
            <GdxImageInner key={x} value={x} {...restProps}></GdxImageInner>
        ))
    }
    return <GdxImageInner value={value} {...restProps}></GdxImageInner>
}