import { useMemo, useState, useEffect, useCallback, cloneElement } from "react";

import { useRemoteTableContext } from "../../context/hook"



export default function GdxFilterItem({ name, children }) {
    const { filter, updateFilter, options: { size } } = useRemoteTableContext();
    const [value, setValue] = useState();
    useEffect(() => {
        if (!filter) return;
        const v = Reflect.get(filter, name);
        setValue(v);
    }, [name, filter, updateFilter])
    const onChange = useCallback((v) => {
        if (v && v.target && v._reactName) {
            v = v.target.value;
        }
        if (v !== undefined) {
            Reflect.set(filter, name, v);
            updateFilter(filter);
        }
    }, [name, filter, updateFilter])
    const ele = useMemo(() => {
        const props = { onChange };
        if (value !== undefined) {
            props.value = value;
        }
        if (size) {
            props.size = size;
        }
        return cloneElement(children, props);
    }, [value, onChange])

    return ele;
}