import { Box, TablePagination } from "@mui/material";

import { useRemoteTableContext } from "../../context/hook";

export default function GdxTableFooter() {
    const { pageSize, pageNo, goPage, totalElements, options: { pages, size, hidePage, footerRender } } = useRemoteTableContext();
    if (hidePage) return null;
    return (
        <Box sx={{ position: 'relative' }}>
            {footerRender && footerRender()}
            {!footerRender &&
                <TablePagination
                    size={size || "medium"}
                    rowsPerPageOptions={pages || [5, 10, 20, 50, 100]}
                    page={pageNo - 1}
                    rowsPerPage={pageSize}
                    count={totalElements}
                    onPageChange={(e, page) => {
                        goPage(page + 1, pageSize)
                    }}
                    onRowsPerPageChange={(e) => {
                        goPage(pageNo, parseInt(e.target.value, 10));
                    }}
                    component="div"
                    sx={{
                        borderTopColor: 'transparent',
                    }}
                />
            }
        </Box>
    )
}