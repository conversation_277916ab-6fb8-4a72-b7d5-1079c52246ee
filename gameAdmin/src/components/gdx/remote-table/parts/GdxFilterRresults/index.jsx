import { useState, useEffect } from 'react';

import { Box, Chip, Paper, Stack, Button } from '@mui/material';

import { Iconify } from 'src/components/iconify';
import GdxValue from 'src/components/gdx/gdx-data/components/GdxValue';

import { useRemoteTableContext } from '../../context/hook';

export default function GdxFilterResults() {
  const {
    updateFilter,
    totalElements,
    filter,
    options: { filters },
  } = useRemoteTableContext();
  const [vs, setVs] = useState([]);

  useEffect(() => {
    if (!filters || !filters.length) {
      return;
    }
    const ss = filters
      .map((f) => {
        const v = filter[f.id];
        if (Array.isArray(v) ? v && v.length : v) {
          return { f, v };
        }
        return null;
      })
      .filter((f) => f);
    setVs(ss);
  }, [filter, filters]);
  if (!vs || !vs.length) return null;
  return (
    <Stack spacing={1.5} sx={{ p: 2.5, pt: 0 }}>
      <Box sx={{ typography: 'body2' }}>
        <strong>{totalElements}</strong>
        <Box component="span" sx={{ color: 'text.secondary', ml: 0.25 }}>
          results found
        </Box>
      </Box>

      <Stack flexGrow={1} spacing={1} direction="row" flexWrap="wrap" alignItems="center">
        {vs.map((xx) => (
          <Block key={xx.f.id} label={xx.f.label}>
            <ChipItem f={xx.f} v={xx.v}></ChipItem>
          </Block>
        ))}
        <Button
          color="error"
          onClick={() => {
            updateFilter({});
          }}
          startIcon={<Iconify icon="solar:trash-bin-trash-bold" />}
        >
          Clear
        </Button>
      </Stack>
    </Stack>
  );
}

function ChipItem({ f, v }) {
  const { updateFilter, filter } = useRemoteTableContext();
  if (f.type === 'multi-select') {
    return v.map((xv, index) => (
      <Chip
        size="small"
        key={index}
        label={<GdxValue value={xv} type={f.dataType} />}
        onDelete={() => {
          updateFilter({
            ...filter,
            ...{ [f.id]: (filter[f.id] || []).filter((xx) => xx !== xv) },
          });
        }}
      />
    ));
  }
  if (f.type !== 'text') {
    return (
      <Chip
        size="small"
        label={<GdxValue value={v} type={f.dataType || f.type} />}
        onDelete={() => {
          updateFilter({ ...filter, ...{ [f.id]: undefined } });
        }}
      />
    );
  }
  return (
    <Chip
      size="small"
      label={v}
      onDelete={() => {
        updateFilter({ ...filter, ...{ [f.id]: undefined } });
      }}
    />
  );
}

function Block({ label, children, sx, ...other }) {
  return (
    <Stack
      component={Paper}
      variant="outlined"
      spacing={1}
      direction="row"
      sx={{
        p: 1,
        borderRadius: 1,
        overflow: 'hidden',
        borderStyle: 'dashed',
        ...sx,
      }}
      {...other}
    >
      <Box component="span" sx={{ typography: 'subtitle2' }}>
        {label}
      </Box>

      <Stack spacing={1} direction="row" flexWrap="wrap">
        {children}
      </Stack>
    </Stack>
  );
}
