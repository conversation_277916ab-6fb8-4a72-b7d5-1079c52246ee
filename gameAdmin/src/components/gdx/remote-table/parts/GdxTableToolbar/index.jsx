import { useMemo, useState, useEffect, useCallback } from 'react';

import { Box, Grid, Stack, Button, Divider, ButtonGroup } from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { useLangTranslate } from 'src/components/gdx/gdx-data/context/common';

import GdxFilterPanel from '../GdxFilterPanel';
import FilterItemWrapper from '../FilterItemWrapper';
import { useRemoteTableContext } from '../../context/hook';

export default function GdxTableToolbar() {
  const { rl } = useLangTranslate();
  const context = useRemoteTableContext();
  const {
    options: { filters, topActions, onTopAction, expandFilterStart, hideToolbar },
    setEditAction,
  } = context;
  const [actionDatas, setActionDatas] = useState([]);
  const onActionWrap = useCallback(
    async (act) => {
      if (onTopAction) {
        if (await onTopAction(act, context)) {
          return;
        }
      }
      if (act.id === 'new' || act.type === 'new') {
        setEditAction('new', {});
      }
    },
    [context]
  );
  useEffect(() => {
    const acts = topActions?.map((x) => {
      if (x === 'new') {
        x = { id: 'new', icon: 'mingcute:add-line', label: rl('New') };
      } else if (x === 'divider') {
        return x;
      }
      return x;
    });
    setActionDatas(acts);
  }, [topActions]);
  const filter = useMemo(() => {
    if (!filters || !filters.length) {
      return null;
    }
    const fs =
      expandFilterStart && filters.length > expandFilterStart
        ? filters.slice(0, expandFilterStart)
        : filters;
    return (
      <Grid container spacing={1}>
        {fs.map((f) => (
          <Grid item key={f.id} xs={4} md={3}>
            <FilterItemWrapper f={f} sx={{ width: 1 }}></FilterItemWrapper>
          </Grid>
        ))}
      </Grid>
    );
  }, [filters]);
  if (hideToolbar) return;
  return (
    <>
      <Stack
        spacing={2}
        alignItems={{ xs: 'flex-end', md: 'center' }}
        justifyContent="space-between"
        direction="column"
        sx={{
          p: 2.5,
          // pr: { xs: 2.5, md: 1 },
        }}
      >
        {filter || (
          <Box
            sx={{
              flexShrink: 0,
              width: { xs: 1, md: 'auto' },
            }}
          >
            {' '}
          </Box>
        )}

        <Stack
          direction="row"
          alignItems="center"
          justifyContent="flex-end"
          spacing={1}
          flexGrow={1}
          sx={{ width: 1 }}
        >
          {expandFilterStart && <GdxFilterPanel></GdxFilterPanel>}
          <ButtonGroup>
            {(actionDatas || []).map((ad, index) => {
              if (ad === 'divider') {
                return (
                  <Divider orientation="vertical" key={index} sx={{ borderStyle: 'dashed' }} />
                );
              }
              const { id, icon, label, endIcon, ...restProps } = ad;
              const ret = (
                <Button
                  key={id}
                  variant="contained"
                  startIcon={<Iconify icon={icon} />}
                  endIcon={<Iconify icon={endIcon} />}
                  onClick={() => {
                    onActionWrap(ad);
                  }}
                  {...restProps}
                >
                  {label}
                </Button>
              );
              return ret;
            })}
          </ButtonGroup>
          {/* <IconButton onClick={popover.onOpen}>
                        <Iconify icon="eva:more-vertical-fill" />
                    </IconButton> */}
        </Stack>
      </Stack>

      {/* <CustomPopover
                open={popover.open}
                onClose={popover.onClose}
                arrow="right-top"
                sx={{ width: 140 }}
            >
                <MenuItem
                    onClick={() => {
                        popover.onClose();
                    }}
                >
                    <Iconify icon="solar:printer-minimalistic-bold" />
                    Print
                </MenuItem>

                <MenuItem
                    onClick={() => {
                        popover.onClose();
                    }}
                >
                    <Iconify icon="solar:import-bold" />
                    Importimport { Label } from 'src/components/label';

                </MenuItem>

                <MenuItem
                    onClick={() => {
                        popover.onClose();
                    }}
                >
                    <Iconify icon="solar:export-bold" />
                    Export
                </MenuItem>
            </CustomPopover> */}
    </>
  );
}
