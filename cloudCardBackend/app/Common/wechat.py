import httpx
from typing import Optional
from pydantic import BaseModel


class WechatTokenResponse(BaseModel):
    access_token: str
    expires_in: int
    errcode: Optional[int] = None
    errmsg: Optional[str] = None


async def get_stable_access_token() -> WechatTokenResponse:
    url = "https://api.weixin.qq.com/cgi-bin/stable_token"
    params = {
        "grant_type": "client_credential",
        "appid": "wx14ae29e39014d908",
        "secret": "1f9d4982bfe3c6dba2663855aec4e22d",
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(url, json=params)
        response.raise_for_status()
        return WechatTokenResponse(**response.json())


class WechatGameResponse(BaseModel):
    errcode: int
    errmsg: str
    has_next: bool
    next_offset: str
    app_list: list


async def get_game(offset: str = None) -> WechatGameResponse:
    data = await get_stable_access_token()
    access_token = data.access_token
    url = (
        "https://api.weixin.qq.com/wxa/servicemarket/game/getminigamecpslist?access_token="
        + access_token
    )
    params = {
        "offset": offset,
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(url, json=params)
        response.raise_for_status()
        return WechatGameResponse(**response.json())
