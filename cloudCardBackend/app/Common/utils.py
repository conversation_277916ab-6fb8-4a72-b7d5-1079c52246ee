import hashlib
import jwt
import time
from bson import ObjectId
from datetime import datetime, timedelta, timezone
from app.Common.db import user, card

# JWT 配置
JWT_SECRET = "yinshiwl"  # 请替换为实际的密钥
JWT_ALGORITHM = "HS256"
JWT_EXPIRE_MINUTES = 60 * 24 * 1  # 1天过期

# 创建JWT访问令牌
def create_access_token(data: dict) -> str:
    """
    创建JWT访问令牌

    Args:
        data: 要编码到token中的数据

    Returns:
        JWT token字符串
    """
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + timedelta(minutes=JWT_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET, algorithm=JWT_ALGORITHM)
    return encoded_jwt


# 验证JWT令牌
def verify_access_token(authorization: str) -> dict:
    """
    验证JWT令牌

    Args:
        authorization: 包含JWT令牌的字符串，可能带有Bearer前缀

    Returns:
        解码后的payload

    Raises:
        ReturnObject: 当token无效或过期时
    """
    try:
        # 移除 Bearer 前缀
        access_token = authorization
        if authorization and authorization.startswith("Bearer "):
            access_token = authorization[7:]

        payload = jwt.decode(access_token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise ReturnObject(status=401, message="Token已过期")
    except jwt.PyJWTError:
        raise ReturnObject(status=401, message="无效的Token")


# 检查APP用户是否登录（使用token字段）
async def hasLogin(token: str, find: bool = True):
    """
    检查APP用户是否登录，并验证用户是否存在

    Args:
        token: 请求头中的token字段值
        find: 是否查询数据库验证用户存在

    Returns:
        True: 如果用户已登录且存在

    Raises:
        ReturnObject: 当用户未登录或不存在时
    """
    if not token:
        raise ReturnObject(status=401, message="请先登录")

    if find:
        find_result = await user.find_one({"token": token})
        if not find_result:
            raise ReturnObject(status=401, message="用户不存在或未登录")
    return True


# 返回数据模型
class ReturnObject(Exception):
    STATUS_MESSAGES = {
        -1: "请求失败",
        0: "请求成功",
        401: "请先登录",
        404: "数据不存在",
        500: "服务器异常",
        9999: "未知错误",
    }

    def __init__(
        self,
        status: int = 9999,
        model: object = None,
        page: object = None,
        message: str = None,
        notReturnToken: bool = True,
    ):
        # 根据 model 设置 status
        if model:
            status = 0
            # 对数据进行格式化
            model = formatModel(model, notReturnToken)
            self.model = model

        if page:
            status = 0
            # 对数据进行格式化
            page["content"] = formatModels(page["content"], notReturnToken)
            self.page = page

        # 如果没有传递 message，则根据 status 设定 message
        if not message:
            message = self.STATUS_MESSAGES.get(status, "未知错误")

        self.status = status
        self.message = message


# 获取毫秒级的时间戳
def getMillisecond():
    return int(round(time.time() * 1000))


# 获取秒级的时间戳
def getSecond():
    return int(round(time.time()))


# 格式化 model 数据
def formatModel(model, notReturnToken=True):
    if model:
        # 如果是 ObjectId，转换为字符串
        if "_id" in model:
            model["id"] = str(model["_id"])
            del model["_id"]
        # 如果不需要返回 token，则删除 token
        if notReturnToken is True and "token" in model:
            del model["token"]
        if notReturnToken is True and "accessToken" in model:
            del model["accessToken"]
        # 不返回创建者的 token
        if "createdToken" in model:
            del model["createdToken"]
        if "password" in model:
            del model["password"]
    return model


# 格式化 model 列表数据
def formatModels(models, notReturnToken=True):
    # 如果 models 是单个对象而不是列表，直接格式化并返回
    if not isinstance(models, list):
        return formatModel(models, notReturnToken)

    # 处理列表
    for i in range(len(models)):
        models[i] = formatModel(models[i], notReturnToken)
    return models


# 获取 uuid 随机数 (32位)
def getUUID():
    import uuid

    return str(uuid.uuid4())


# sha256 加密
def sha256_hash(text):
    # 创建一个 SHA-256 哈希对象
    sha256 = hashlib.sha256()

    # 更新哈希对象的内容
    sha256.update(text.encode("utf-8"))

    # 获取散列值的十六进制表示
    return sha256.hexdigest()


# 查询名片的创建者
async def findCardCreatedToken(cardId: str):
    find_result = await card.find_one({"_id": ObjectId(cardId)})
    if find_result:
        return find_result["token"]
    return None
