import motor.motor_asyncio

# 配置 MongoDB 连接
MONGO_DETAILS = "mongodb://127.0.0.1:27017"  # 测试环境
client = motor.motor_asyncio.AsyncIOMotorClient(MONGO_DETAILS)

# 配置 MongoDB 数据库
database = client.cloudCard  # 数据库

# 配置 MongoDB 集合
card = database.card  # 名片
user = database.user  # 用户
account = database.account  # 账户
cardBrowse = database.cardBrowse  # 名片浏览
cardLike = database.cardLike  # 名片点赞
cardCollect = database.cardCollect  # 名片收藏
file = database.file  # 文件
carousel = database.carousel  # 轮播图


love = database.love  # 恋爱清单