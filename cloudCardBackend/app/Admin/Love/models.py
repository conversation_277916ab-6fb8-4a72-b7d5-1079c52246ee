from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from app.Common.base_models import BaseEntity, PageQuery, timeRange

# 实体模型
class LoveEntity(BaseEntity):
    title: str = Field(..., description="标题")
    status: str = Field(
        "UNFINISHED", description="状态: UNFINISHED-未完成, FINISHED-已完成"
    )
    finishTime: Optional[int] = Field(None, description="完成时间")
    photos: Optional[List[str]] = Field(None, description="照片")
    yLog: Optional[str] = Field(None, description="小徐日志")
    xLog: Optional[str] = Field(None, description="小张日志")
    commentCount: int = Field(0, description="评论数")


# 分页查询参数
class LovePageQuery(PageQuery):
    id: Optional[str] = Field(None, description="记录ID", query_type="exact")
    title: Optional[str] = Field(None, description="标题", query_type="like")
    status: Optional[str] = Field(
        None, description="状态: UNFINISHED-未完成, FINISHED-已完成", query_type="exact"
    )
    finishTime: Optional[timeRange] = Field(
        None, description="完成时间", query_type="date_range"
    )
    createdAt: Optional[timeRange] = Field(
        None, description="创建时间", query_type="date_range"
    )
