from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from app.Common.base_models import BaseEntity, PageQuery


# 实体模型
class LoveEntity(BaseEntity):
    title: str = Field(..., description="标题")
    status: str = Field("UNFINISHED", description="状态: UNFINISHED-未完成, FINISHED-已完成")
    finishTime: Optional[datetime] = Field(None, description="完成时间")



# 分页查询参数
class LovePageQuery(PageQuery):
    id: Optional[str] = Field(None, description="记录ID")
    title: Optional[str] = Field(None, description="标题")
    status: Optional[str] = Field(None, description="状态: UNFINISHED-未完成, FINISHED-已完成")
