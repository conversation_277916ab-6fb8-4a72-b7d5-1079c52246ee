from pydantic import Field
from typing import Optional
from app.Common.base_models import BaseEntity, PageQuery


# 实体模型
class LoveCommentEntity(BaseEntity):
    loveId: Optional[str] = Field(..., description="恋爱清单ID")
    content: Optional[str] = Field(..., description="评论内容")
    name: Optional[str] = Field(..., description="评论人")


# 分页查询参数
class LoveCommentPageQuery(PageQuery):
    loveId: Optional[str] = Field(..., description="恋爱清单ID", query_type="exact")
