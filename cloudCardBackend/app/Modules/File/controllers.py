import os
from fastapi import APIRouter, File, HTTPException, Header, UploadFile
from fastapi.responses import FileResponse, PlainTextResponse
from pymongo import ReturnDocument
from app.Common.utils import ReturnObject, getMillisecond, getUUID, hasLogin
from app.Common.db import file as fileDB

fileRouter = APIRouter()


# 文件上传
@fileRouter.post("/upload")
async def file_upload(token: str = Header(None), file: UploadFile = File(...)):
    try:
        await hasLogin(token)

        if not file.content_type.startswith("image"):
            raise ReturnObject(status=-1, message="不支持的文件类型")

        MAX_FILE_SIZE = 2 * 1024 * 1024  # 2MB

        # 读取文件内容
        file_content = await file.read()

        # 获取文件大小
        file_size = len(file_content)
        if file_size > MAX_FILE_SIZE:
            raise ReturnObject(status=-1, message="文件大小不能超过 2MB")

        # 计算 token 的 SHA-256 哈希值
        name = getUUID()

        # 设置保存文件的路径
        file_path = os.path.join(
            "assets/files", f"{name}.{file.content_type.split('/')[1]}"
        )

        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        # 保存文件到本地
        with open(file_path, "wb") as f:
            f.write(file_content)

        # 保存文件数据
        current_time = getMillisecond()
        save_data = {
            "name": file.filename,
            "path": file_path,
            "size": file_size,
            "token": token,
            "createdTime": current_time,
            "type": file.content_type,
            "fileId": name,
        }
        result = await fileDB.insert_one(save_data)
        if result.inserted_id:
            raise ReturnObject(
                model={"fileId": name, "size": file_size}, message="Success"
            )

        raise ReturnObject(status=-1, message="Error")
    except ReturnObject as e:
        return e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@fileRouter.get("/{fileId}")
async def file(fileId: str, token: str = Header(None)):
    try:
        current_time = getMillisecond()
        result = await fileDB.find_one_and_update(
            {"fileId": fileId},
            {"$set": {"time": current_time}},
            return_document=ReturnDocument.AFTER,
        )
        if result:
            path = result["path"]
            return FileResponse(path)
        raise HTTPException(status_code=404, detail="文件不存在")
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
